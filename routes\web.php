<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Auth\ConfirmPasswordController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Controllers\Auth\EmailVerificationController;
use App\Http\Controllers\SitemapController;

// Password confirmation routes
Route::get('/confirm-password', [ConfirmPasswordController::class, 'showConfirmForm'])
    ->middleware('auth')
    ->name('password.confirm');

Route::post('/confirm-password', [ConfirmPasswordController::class, 'confirm'])
    ->middleware(['auth', 'throttle:50,1']);

// Email Verification Routes
Route::get('/email/verify', [EmailVerificationController::class, 'notice'])
    ->middleware('auth')
    ->name('verification.notice');

Route::get('/email/verify/{id}/{hash}', [EmailVerificationController::class, 'verify'])
    ->middleware(['auth', 'signed'])
    ->name('verification.verify');

Route::post('/email/verification-notification', [EmailVerificationController::class, 'send'])
    ->middleware(['auth', 'throttle:6,1'])
    ->name('verification.send');

// Frontend routes
Route::get('/', [\App\Http\Controllers\Frontend\HomeController::class, 'index'])->name('home');
Route::get('/news/{slug}', [\App\Http\Controllers\Frontend\NewsController::class, 'show'])->name('news.show');
Route::get('/category/{slug}', [\App\Http\Controllers\Frontend\CategoryController::class, 'show'])->name('category.show');
Route::get('/tags/{slug}', [App\Http\Controllers\Frontend\TagController::class, 'show'])->name('tag.show');
Route::post('/load-more-news', [App\Http\Controllers\Frontend\HomeController::class, 'loadMoreNews'])->name('home.load-more');
Route::get('/privacy-policy', [App\Http\Controllers\Frontend\HomeController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-and-conditions', [App\Http\Controllers\Frontend\HomeController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('/contact', [App\Http\Controllers\ContactController::class, 'index'])->name('contact');
Route::post('/contact', [App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');
Route::get('/about', [App\Http\Controllers\Frontend\HomeController::class, 'about'])->name('about');



Route::middleware('guest')->group(function () {
    // Login Routes
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Forgot Password Routes
    Route::get('/forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLinkEmail'])->name('password.email');

    // Reset Password Routes
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetForm'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');

});

// Logout Route (for authenticated users)
Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'throttle:60,1', 'verified', 'password.confirm'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // User CRUD Routes - Using index for both view and datatable
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);

    // Role CRUD Routes
    Route::resource('roles', \App\Http\Controllers\Admin\RoleController::class);

    // Permission CRUD Routes
    Route::resource('permissions', \App\Http\Controllers\Admin\PermissionController::class);

    // Category CRUD Routes
    Route::resource('categories', \App\Http\Controllers\Admin\CategoryController::class);

    // Tag CRUD Routes
    Route::resource('tags', \App\Http\Controllers\Admin\TagController::class);

    // News CRUD Routes
    Route::resource('news', \App\Http\Controllers\Admin\NewsController::class);

    // Profile Routes
    Route::get('/profile', [\App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [\App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [\App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('profile.update');

    // Settings Routes
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
    Route::post('/settings/clear-cache', [\App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::post('/settings/test-email', [\App\Http\Controllers\Admin\SettingsController::class, 'testEmail'])->name('settings.test-email');
    Route::post('/settings/logout-all-devices', [\App\Http\Controllers\Admin\SettingsController::class, 'logoutAllDevices'])->name('settings.logout-all-devices');
    Route::post('/settings/emergency-logout-all', [\App\Http\Controllers\Admin\SettingsController::class, 'emergencyLogoutAll'])->name('settings.emergency-logout-all');

    // Login Logs Routes
    Route::get('/login-logs', [\App\Http\Controllers\Admin\LoginLogController::class, 'index'])->name('login-logs.index');
    Route::post('/login-logs/clear-attempts/{email}', [\App\Http\Controllers\Admin\LoginLogController::class, 'clearAttempts'])->name('login-logs.clear-attempts');
    Route::post('/login-logs/reactivate/{email}', [\App\Http\Controllers\Admin\LoginLogController::class, 'reactivateAccount'])->name('login-logs.reactivate');

    // News Integration Routes
    Route::prefix('news-integration')->name('news-integration.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\NewsIntegrationController::class, 'index'])->name('index');
        Route::get('/data', [\App\Http\Controllers\Admin\NewsIntegrationController::class, 'getData'])->name('data');
        Route::post('/fetch-news', [\App\Http\Controllers\Admin\NewsIntegrationController::class, 'fetchNews'])->name('fetch-news');
        Route::post('/generate-news', [\App\Http\Controllers\Admin\NewsIntegrationController::class, 'generateNews'])->name('generate-news');
        Route::post('/generate-single/{id}', [\App\Http\Controllers\Admin\NewsIntegrationController::class, 'generateSingle'])->name('generate-single');
        Route::get('/stats', [\App\Http\Controllers\Admin\NewsIntegrationController::class, 'getStats'])->name('stats');
    });

    // Pending News Routes
    Route::prefix('pending-news')->name('pending-news.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\PendingNewsController::class, 'index'])->name('index');
        Route::get('/stats', [\App\Http\Controllers\Admin\PendingNewsController::class, 'getStats'])->name('stats');
        Route::get('/{news}', [\App\Http\Controllers\Admin\PendingNewsController::class, 'show'])->name('show');
        Route::post('/{news}/approve', [\App\Http\Controllers\Admin\PendingNewsController::class, 'approve'])->name('approve');
        Route::post('/{news}/reject', [\App\Http\Controllers\Admin\PendingNewsController::class, 'reject'])->name('reject');
        Route::post('/bulk-approve', [\App\Http\Controllers\Admin\PendingNewsController::class, 'bulkApprove'])->name('bulk-approve');
    });

    // Contact management routes
    Route::resource('contacts', \App\Http\Controllers\Admin\ContactController::class)->only(['index', 'show', 'destroy']);
    Route::patch('/contacts/{contact}/status', [\App\Http\Controllers\Admin\ContactController::class, 'updateStatus'])->name('contacts.update-status');

    // Log Viewer Routes
    Route::get('/logs', [\App\Http\Controllers\LogViewerController::class, 'index'])->name('logs.index');
    Route::get('/logs/fetch', [\App\Http\Controllers\LogViewerController::class, 'fetch'])->name('logs.fetch');
    Route::post('/logs/delete-monthly', [\App\Http\Controllers\LogViewerController::class, 'deleteMonthlyLog'])->name('logs.delete-monthly');
    // Add more admin routes here as needed
});

// Search routes
Route::post('/search', [App\Http\Controllers\Frontend\SearchController::class, 'search'])->name('search');
Route::get('/search', [App\Http\Controllers\Frontend\SearchController::class, 'searchPage'])->name('search.page');
Route::post('/search/load-more', [App\Http\Controllers\Frontend\SearchController::class, 'loadMoreResults'])->name('search.load-more');

// Public News Integration Routes (for development/cron access)
Route::prefix('api/news')->name('api.news.')->group(function () {
    Route::get('/test', function () {
        return response()->json([
            'success' => true,
            'message' => 'API is working',
            'timestamp' => now()->toDateTimeString()
        ]);
    })->name('test');

    Route::get('/fetch-latest/{limit?}', [\App\Http\Controllers\Api\NewsIntegrationController::class, 'fetchLatest'])->name('fetch-latest');
    Route::get('/generate-content/{limit?}', [\App\Http\Controllers\Api\NewsIntegrationController::class, 'generateContent'])->name('generate-content');

    Route::get('/status', [\App\Http\Controllers\Api\NewsIntegrationController::class, 'status'])->name('status');
});

// Sitemap route
Route::get('/sitemap.xml', [SitemapController::class, 'index']); // sitemap index
Route::get('/sitemap_{type}_{chunk}.xml', [SitemapController::class, 'chunked'])->where('chunk', '[0-9]+');
Route::get('/robots.txt', [SitemapController::class, 'robots']);
